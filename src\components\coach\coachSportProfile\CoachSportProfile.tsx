'use client'
import BackButton from "@/components/common/BackButton";
import MultiSelectWithChip from "@/components/common/MultiSelectWithChip";
import ProfileUrl from "@/components/common/ProfileUrl";
import SectionsLinkTracker from "@/components/common/SectionsLinkTracker";
import SportHighLightLinks from "@/components/common/SportHighLightLinks";
import SportHighLightVideos from "@/components/common/SportHighLightVideos";
import { Switch } from "@/components/ui/switch";
import { RootState } from "@/store";
import { handleCoachSportInputChange } from "@/store/slices/coach/coachSportSlice";
import { Option } from "@/utils/interfaces";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import CoachScoreCard from "./CoachScoreCard";
import CoachSportDetails from "./CoachSportDetails";
import CoachStatsForm from "./CoachStatsForm";
import ImageGallery from "./ImageGallery";
import TeamsCoaching from "./TeamsCoaching";
import TrackRecord from "./TrackRecord";
import ViewScoreCard from "./ViewScoreCard";
import { Params } from "next/dist/shared/lib/router/utils/route-matcher";

interface IProps {
    params: Params;
}

const linkTrackList = [
    { id: 'coachSportOverview', img: '/overview.svg', label: 'Overview', },
    { id: 'coachSportTeams', img: '/teams.png', label: 'Teams', },
    { id: 'coachSportStats', img: '/stats.png', label: 'Stats', },
    { id: 'coachSportTrackRecord', img: '/track-record.png', label: 'Track Record', },
    { id: 'coachSportsMedia', img: '/media.png', label: 'Media', },
];

const CoachSportProfile = ({ params }: IProps) => {
    const { toggleStrengths, addedUniqueStrengths, addedStatsScoreCardList,
        scoreCardId, toggleVideoSection, highLightVideoData, addedHighLightVideosList,
        toggleHighLightLinks, isEditHighlightLinks, highLightLinksList,
    } = useSelector((state: RootState) => state.coachSport)
    const { allUniqueStrengthsList } = useSelector((state: RootState) => state.commonSlice)
    const dispatch = useDispatch();

    const handleOnChange = (name: string, value: Option[] | boolean | string) => {
        dispatch(handleCoachSportInputChange({ name, value }))
    }

    const handleUpdateLatestVideoValues = (name, value) => {
        if (name === 'video' && (typeof value === 'object')) {
            if (value as File) {
                const validTypes = ['video/mp4'];
                if (validTypes.includes(value.type)) {
                    dispatch(handleCoachSportInputChange({ name: 'highLightVideoData', value: { ...highLightVideoData, video: value } }));
                } else {
                    toast.error('Invalid file type. Please upload MP4.');
                }
            }
        } else if (name === 'toggleVideoSection') {
            dispatch(handleCoachSportInputChange({ name, value }))
        } else {
            dispatch(handleCoachSportInputChange({ name: 'highLightVideoData', value: { ...highLightVideoData, [name]: value } }));
        }
    }

    const handleSaveHighlightVideo = () => {
        dispatch(handleCoachSportInputChange({ name: "addedHighLightVideosList", value: [...addedHighLightVideosList, { id: `${Date.now()}-${Math.floor(Math.random() * 10000)}`, ...highLightVideoData }] }))
        dispatch(handleCoachSportInputChange({ name: 'highLightVideoData', value: { title: '', aboutVideo: "", video: null } }));
    }

    const handleEditHighLightVideo = (id: number) => {
        //Pick id and Edit
    }

    const handleDeleteHighLightVideo = (id: number) => {
        //Pick id and Delete
    }

    const handleSaveHighLightLinks = (list) => {
        dispatch(handleCoachSportInputChange({ name: 'highLightLinksList', value: list }))
    }

    return (
        <>
            <div className="grid grid-cols-1 md:grid-cols-4 flex-grow w-full gap-3">
                <div className="hidden md:block md:col-span-1" />

                <div className="flex flex-col gap-3 py-5 col-span-1 md:col-span-2">
                    <BackButton />

                    <h3 className="text-2xl text-secondary font-bold text-center">Coach Profile - {params?.sportName}</h3>

                    <div className="flex flex-col gap-11">
                        

                        <SectionsLinkTracker linkTrackList={linkTrackList} />

                        <CoachSportDetails />

                        <div id='coachSportTeams'>
                            <TeamsCoaching />
                        </div>

                        {/* Strengths */}
                        <div className="bg-slate-100 rounded-lg p-5 flex flex-col gap-5">
                            <div className="flex items-center justify-center gap-5">
                                <h2 className="text-xl font-bold">Unique Coaching Strengths</h2>
                                <Switch
                                    checked={toggleStrengths}
                                    onCheckedChange={(checked) => handleOnChange('toggleStrengths', checked)} />
                            </div>
                            {toggleStrengths ?
                                <MultiSelectWithChip
                                    options={allUniqueStrengthsList}
                                    value={addedUniqueStrengths}
                                    name='addedUniqueStrengths'
                                    onChange={(selected) => handleOnChange('addedUniqueStrengths', selected)}
                                    placeholder="Select Strengths..."
                                /> : null}
                        </div>

                        <div id='coachSportStats'>
                            <CoachStatsForm />
                        </div>

                        {addedStatsScoreCardList?.length > 0 &&
                            <div id='statsScoreCard' className="flex flex-col gap-5">
                                <CoachScoreCard />
                                {scoreCardId && <ViewScoreCard sportName={params?.sportName} />}
                            </div>}

                        <div id='coachSportTrackRecord'>
                            <TrackRecord />
                        </div>

                        <SportHighLightVideos
                            params={params}
                            toggleVideoSection={toggleVideoSection}
                            latestVideoData={highLightVideoData}
                            addedHighLightVideoList={addedHighLightVideosList}
                            handleUpdateLatestVideoValues={handleUpdateLatestVideoValues}
                            handleSaveHighlightVideo={handleSaveHighlightVideo}
                            handleEditHighLightVideo={handleEditHighLightVideo}
                            handleDeleteHighLightVideo={handleDeleteHighLightVideo}
                        />

                        <SportHighLightLinks
                            toggleHighlightLinks={toggleHighLightLinks}
                            isEditHighlightLinks={isEditHighlightLinks}
                            highlightLinksList={highLightLinksList}
                            handleOnChange={handleOnChange}                            
                        />

                        <div id='coachSportsMedia'>
                            <ImageGallery />
                        </div>
                    </div>
                </div>

                <div className="hidden md:block md:col-span-1" />
            </div>
        </>
    )
}
export default CoachSportProfile