import { configureStore } from "@reduxjs/toolkit";
import athleteHome from "./slices/athlete/athleteHomeSlice";
import athleteProfileSlice from "./slices/athlete/athleteProfileSlice";
import athleteSportProfileSlice from "./slices/athlete/athleteSportProfileSlice";
import loginSlice from "./slices/auth/loginSlice";
import registerSlice from "./slices/auth/registerSlice";
import announcementDashboard from "./slices/business/announcement";
import orgProfile from "./slices/business/sportsBusiness";
import coachProfileSlice from "./slices/coach/coachProfileSlice";
import coachSportSlice from "./slices/coach/coachSportSlice";
import commonSlice from "./slices/commonSlice";
import homeCMSReducer from "./slices/homeCMSSlice";
import premiumSlice from "./slices/premiumSlice";

export const store = configureStore({
  reducer: {
    login: loginSlice,
    commonSlice: commonSlice,
    athleteProfile: athleteProfileSlice,
    athleteSportProfile: athleteSportProfileSlice,
    announcementDashboard: announcementDashboard,
    coachProfile: coachProfileSlice,
    orgProfile: orgProfile,
    register: registerSlice,
    coachSport: coachSportSlice,
    athleteHome: athleteHome,
    homeCMS: homeCMSReducer,
    premium: premiumSlice,
  },
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
