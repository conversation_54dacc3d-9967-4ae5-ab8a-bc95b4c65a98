import { Button } from "@/components/ui/button"
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo"
import { handleUpdateUserInput } from "@/store/slices/athlete/athleteProfileSlice"
import { generateUpdateProfileUrl } from "@/utils/commonFunctions"
import { ROLES } from "@/utils/constants"
import { EachSportItem } from "@/utils/interfaces"
import { PencilLine } from "lucide-react"
import { useRouter } from "next/navigation"
import { useDispatch } from "react-redux"

interface IProps {
    item: EachSportItem
    roleId?: number
}

const SportItem = ({ item, roleId }: IProps) => {
    const { profileData } = useLocalStoredInfo()
    const profileUrl = profileData && generateUpdateProfileUrl(profileData)
    const dispatch = useDispatch()
    const router = useRouter()

    const handleSelectSportItem = () => {
        router.push(`${profileUrl}/${item?.sportName}/${item?.sportId}`)
        dispatch(handleUpdateUserInput({
            name: 'sportProfileId', value: item?.sportId
        }))
    }

    console.log(item, "item");

    console.log(roleId,"roleId",ROLES.COACH)

    return (
        <>
            <div className="grid grid-cols-1 sm:grid-cols-5 justify-center items-start flex-wrap gap-1 bg-white rounded-lg shadow-md border border-slate-100 hover:shadow-xl hover:border-slate-300 p-4">
                <span className={`text-secondary font-semibold text-center`}>{item?.isPrimary ? 'Primary' : ''}</span>
                <div onClick={handleSelectSportItem} className="flex flex-col items-center gap-3 sm:col-span-3 cursor-pointer">
                    {roleId === ROLES.COACH ? <p className="font-semibold text-center">{item?.sportName} kkkk</p> : 
                    <p className="font-semibold text-center">{item?.sportLevel ? `${item?.sportName} - ${item?.sportLevel}` : item?.sportName}</p>}
                    <p className="text-center">{item?.specilities?.map(each => each?.specilityName)?.join(" | ")}</p>
                </div>
                <div className="text-center">
                    <Button variant={'outline'} size={'icon'} onClick={handleSelectSportItem}>
                        <PencilLine />
                    </Button>
                </div>
            </div>
        </>
    )
}
export default SportItem