/* Festive is a colour that i have defined accoring to the tailwind config and */
@tailwind base;
@tailwind components;
@tailwind utilities;
@import url('https://fonts.googleapis.com/css2?family=Raleway:ital,wght@0,100..900;1,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap');

body {
  font-family: "Raleway", sans-serif;
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}
@layer base {
  :root {
    --background: 210 100% 98%; /* blue-50 */
    --foreground: 224 64% 33%; /* blue-900 */
    --card: 0 0% 100%;
    --card-foreground: 224 64% 33%; /* blue-900 */
    --popover: 0 0% 100%;
    --popover-foreground: 224 64% 33%; /* blue-900 */

    /* Updated primary */
    --primary: 226 61% 22%; /* #172554 */
    --primary-foreground: 0 0% 100%;

    --secondary: 28 89% 55%; /* #f08424 */
    --secondary-foreground: 0 0% 100%;
    --muted: 210 100% 98%; /* blue-50 */
    --muted-foreground: 215 20% 65%; /* blue-400 */
    --accent: 210 100% 98%; /* blue-50 */
    --accent-foreground: 217 91% 60%; /* blue-500 */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 214 32% 91%; /* blue-200 */
    --input: 214 32% 91%; /* blue-200 */
    --ring: 226 61% 22%; /* Match primary */
    --radius: 0.5rem;
  }
  

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;

    /* Updated primary */
    --primary: 226 61% 22%; /* #172554 */
    --primary-foreground: 0 0% 100%;

    --secondary: 28 89% 55%; /* #f08424 */
    --secondary-foreground: 0 0% 100%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 226 61% 22%; /* Match primary */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@keyframes gradient-x {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient-x {
  background-size: 200% 200%;
  animation: gradient-x 15s ease infinite;
}

body {
  padding: 0;
  margin: 0;
  scrollbar-width: none;
  scrollbar-color: #2a2e70 transparent;
}

::-webkit-scrollbar {
  width: 10px;
}
::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: #2a2e70;
  width: inherit;
  border-radius: 10px;
}

/* To adjust the top position of the sticky CTA bar */
@media (max-width: 1050px) {
  .sticky-cta-top {
    top: 60px !important;
  }
}
