import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>er,
    <PERSON><PERSON><PERSON><PERSON>er,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Di<PERSON>Trigger
} from "@/components/ui/dialog"
import {
    <PERSON><PERSON><PERSON>,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip"
import { useTokenValues } from "@/hooks/useTokenValues"
import { AppDispatch } from "@/store"
import { deleteAthleteGalleryItem, fetchAthleteGallery, postAthleteGallery } from "@/store/slices/athlete/athleteProfileSlice"
import { ROLES } from "@/utils/constants"
import { ProfileGalleryItem } from "@/utils/interfaces"
import { Loader, Plus, Trash2 } from "lucide-react"
import { ChangeEvent, useEffect, useState } from "react"
import { useDispatch } from "react-redux"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import { Label } from "../ui/label"
import { Skeleton } from "../ui/skeleton"
import { Switch } from "../ui/switch"
import AlertPopup from "./AlertPopup"
import ImagesUpload from "./ImagesUpload"
import { deleteCoachGalleryItem, fetchCoachGallery, postCoachGallery } from "@/store/slices/coach/coachProfileSlice"

interface IProps {
    toggleGallery: boolean;
    handleToggleSection: (name: string, check: boolean) => void;
    list: ProfileGalleryItem[];
    loading?: boolean;
    origin: string;
    fetchLoading?: boolean;
}

const emptyGallery = {
    image: [] as any,
    title: ''
}


const ProfilesGallery = ({ toggleGallery, handleToggleSection, list, loading, origin, fetchLoading }: IProps) => {
    const [openAdd, setOpenAdd] = useState(false)
    const [addGallery, setAddGallery] = useState(emptyGallery)
    const [galleryList, setGalleryList] = useState(list)
    const dispatch = useDispatch<AppDispatch>()
    const { userId, roleId, isPremiumUser } = useTokenValues()
    const isAthletePremiumUser = roleId === ROLES.ATHLETE && isPremiumUser

    useEffect(() => {
        list && setGalleryList(list)
    }, [list])

    useEffect(() => {
        dispatch(fetchAthleteGallery())
        dispatch(fetchCoachGallery())
    }, [dispatch])

    const handleOnChangeAdd = (event: ChangeEvent<HTMLInputElement>) => {
        const { name, value } = event.target
        setAddGallery((prev) => ({ ...prev, [name]: value }))
    }

    const handleOnChangeImage = (files) => {
        setAddGallery((prev) => ({ ...prev, image: files }))
    }

    const handleOpenGallery = () => {
        setOpenAdd(!openAdd)
        setAddGallery(emptyGallery)
    }

    const apiSuccessStatus = async (api, action, fetchAPI) => {
        try {
            if (api.fulfilled.match(action)) {
                setAddGallery(emptyGallery)
                setOpenAdd(false)
                await dispatch((fetchAPI()))
            }
        } catch (error) {

        }
    };

    const handleAddGalleryItem = async () => {
        const { name, blob } = addGallery?.image[0]
        const formData = new FormData();
        formData.append("userId", userId?.toString()!);
        formData.append("description", addGallery?.title || "");
        formData.append("isActive", "true");
        formData.append("fileType", `${origin}Gallery`);
        formData.append("isApproved", "true");
        formData.append("fileLocation", blob, name);

        switch (origin?.toLowerCase()) {
            case 'athlete':
                const galleryAction = await dispatch(postAthleteGallery(formData));
                apiSuccessStatus(postAthleteGallery, galleryAction, fetchAthleteGallery)
                break;
            case 'coach':
                const coachGalleryAction = await dispatch(postCoachGallery(formData));
                apiSuccessStatus(postCoachGallery, coachGalleryAction, fetchCoachGallery)
                break;
            case 'business':

                break;
            default:
                break;
        }
    }

    const handleDeleteGallery = async (id) => {
        switch (origin?.toLowerCase()) {
            case 'athlete':
                const deleteAction = await dispatch(deleteAthleteGalleryItem(id));
                apiSuccessStatus(deleteAthleteGalleryItem, deleteAction, fetchAthleteGallery)
                break;
            case 'coach':
                const coachDeleteAction = await dispatch(deleteCoachGalleryItem(id));
                apiSuccessStatus(deleteAthleteGalleryItem, coachDeleteAction, fetchCoachGallery)
                break;
            case 'business':

                break;
            default:
                break;
        }
    }

    if (fetchLoading) {
        return (
            <div className="flex flex-col items-center gap-2">
                <Skeleton className="h-40 w-full rounded-lg" />
                <Skeleton className="h-40 w-full rounded-lg" />
                <Skeleton className="h-40 w-full rounded-lg" />
            </div>
        )
    }

    return (
        <>
            <div className="w-full h-full flex flex-col overflow-hidden bg-slate-100 p-4 rounded-lg">
                <div className="flex items-center justify-center gap-4 mb-2 shrink-0">
                    <Dialog open={openAdd} onOpenChange={handleOpenGallery}>
                        <TooltipProvider>
                            <Tooltip>
                                <DialogTrigger asChild
                                    disabled={isAthletePremiumUser ? galleryList?.length >= 20 : galleryList?.length >= 6}>
                                    <TooltipTrigger asChild>
                                        <Button variant={'outline'} size={'icon'}> <Plus /> </Button>
                                    </TooltipTrigger>
                                </DialogTrigger>
                                <TooltipContent>
                                    <p>Add Gallery</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                        <DialogContent onInteractOutside={(event) => event.preventDefault()}>
                            <DialogHeader>
                                <DialogTitle>Add Gallery</DialogTitle>
                            </DialogHeader>
                            <div className="flex flex-col gap-6 p-5 mt-5 h-[300px] overflow-auto">
                                <div className="flex flex-col gap-1">
                                    <Label>Title</Label>
                                    <Input
                                        placeholder="Enter image title"
                                        value={addGallery?.title}
                                        name='title'
                                        onChange={handleOnChangeAdd}
                                        className="bg-white"
                                        maxLength={50}
                                    />
                                </div>
                                <div className="flex flex-col gap-1">
                                    <ImagesUpload
                                        value={addGallery?.image}
                                        onChange={handleOnChangeImage}
                                        maxImages={1}
                                        maxSize={1024}
                                        name={'galleryImage'}
                                        width="w-38"
                                        height="h-54"
                                    />
                                </div>
                            </div>
                            <DialogFooter>
                                <Button type="submit"
                                    disabled={!addGallery?.title || !addGallery?.image}
                                    onClick={handleAddGalleryItem}
                                >
                                    {loading ?
                                        <Loader className="animate-spin text-white w-10 h-10" />
                                        : 'Save'}
                                </Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>

                    <h3 className="font-bold text-xl text-center">Gallery</h3>
                    <Switch
                        name='toggleGallery'
                        checked={toggleGallery}
                        onCheckedChange={(checked) => handleToggleSection('toggleGallery', checked)}
                    />
                </div>

                {toggleGallery && (
                    <div className="max-h-[600px] overflow-y-auto ">
                        <div className="flex flex-col gap-4">
                            {galleryList?.length > 0 ? galleryList?.map((each) => (
                                <div className="flex flex-col" key={each?.id}>
                                    <div className="bg-white hover:shadow-lg  w-full rounded-lg p-1 px-2 flex flex-col items-center gap-4">
                                        <img
                                            src={each?.fileLocation && each?.fileLocation}
                                            alt={each?.description}
                                            className="w-full object-contain max-w-60"
                                        />
                                        <div className="flex items-center justify-between w-full">
                                            <p className="font-bold">{each?.title}</p>
                                            <AlertPopup
                                                trigger={<Button variant={'destructive'} size={'icon'}
                                                >
                                                    <Trash2 />
                                                </Button>
                                                }
                                                alertTitle="Confirm Deletion"
                                                alertContent="Are you sure, you want to delete?"
                                                action={() => handleDeleteGallery(each?.id)}
                                            />
                                        </div>
                                    </div>
                                </div>
                            )) : <div className="flex items-center justify-center min-h-[300px]">
                                <p className="text-gray-500 text-center">Bring this gallery to life by adding something awesome!</p>
                            </div>}
                        </div>
                    </div>
                )}
            </div>
        </>
    )
}
export default ProfilesGallery