import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import GovtIdStepOne from "./GovtIdStepOne"
import VerificationStepTwo from "./VerificationStepTwo"
import { useDispatch, useSelector } from "react-redux"
import { postAddGovtDocument } from "@/store/slices/coach/coachProfileSlice"
import { AppDispatch, RootState } from "@/store"
import { useTokenValues } from "@/hooks/useTokenValues"
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo"
import { toast } from "react-toastify"
import { useState } from "react"

const CoachVerification = () => {
    const dispatch = useDispatch<AppDispatch>()
    const { govtIdData, addedGovtIdData, coachProfileData } = useSelector((state: RootState) => state.coachProfile)
    const { userId, roleId } = useTokenValues()
    const { profileId } = useLocalStoredInfo()
    const [isSubmitting, setIsSubmitting] = useState(false)
    const [checkboxes, setCheckboxes] = useState({
        nameVisible: false,
        certificationValid: false,
        recognizedAuthority: false
    })

    const handleCheckboxChange = (field: keyof typeof checkboxes) => (checked: boolean) => {
        setCheckboxes(prev => ({ ...prev, [field]: checked }))
    }

    const handleSaveDocument = async () => {
        // Validate that we have the required form data
        if (!addedGovtIdData && !govtIdData) {
            toast.error("Please fill out the government ID form first")
            return
        }

        const formData = addedGovtIdData || govtIdData

        if (!formData.title || !formData.documentType || !formData.file) {
            toast.error("Please fill out all required fields")
            return
        }

        // Validate checkboxes
        if (!checkboxes.nameVisible || !checkboxes.certificationValid || !checkboxes.recognizedAuthority) {
            toast.error("Please check all the required boxes before saving")
            return
        }

        setIsSubmitting(true)

        try {
            // Create the payload in the required format
            const payload = {
                userId: Number(userId),
                roleId: Number(roleId),
                coachId: coachProfileData?.id || profileId || Number(userId),
                docTypeId: formData.documentType.value,
                docTypTxt: formData.documentType.label,
                docTitle: formData.title,
                docExpirationDate: formData.expirationDate ? new Date(formData.expirationDate).toISOString() : null,
                docDesc: formData.description || "",
                docLink: "", // Will be populated after file upload
                docFilePath: "", // Will be populated after file upload
                isHidden: false
            }

            // TODO: Handle file upload to S3 and get the URL
            // For now, we'll send the payload without the file URLs
            // You'll need to implement file upload logic here

            const resultAction = await dispatch(postAddGovtDocument(payload))

            if (postAddGovtDocument.fulfilled.match(resultAction)) {
                toast.success("Document saved successfully")
                // Navigate to Declaration or next step
            }
        } catch (error) {
            console.error("Failed to save document:", error)
            toast.error("Failed to save document")
        } finally {
            setIsSubmitting(false)
        }
    }

    return (
        <>
            <div className="flex flex-col bg-slate-100 rounded-lg p-5 gap-10">
                <h3 className="text-xl font-bold text-center">Coach Verification</h3>

                <div className="flex flex-col gap-2">
                    <h2 className="font-bold">
                        Step 1:  {" "}
                        <span className="text-secondary">Please Upload one Goverment ID</span>
                    </h2>
                    <GovtIdStepOne />
                </div>

                <div className="flex flex-col gap-2">
                    <h2 className="font-bold">
                        Step 2:  {" "}
                        <span className="text-secondary"> Please Upload atleast one Additional Document</span>
                    </h2>
                    <VerificationStepTwo />
                </div>

                <div className="flex flex-col gap-2">
                    <h2 className="font-bold">
                        Step 3:  {" "}
                        <span className="text-secondary">Please check these these boxes and then click
                            before uploading these documents</span>
                    </h2>

                    <div className="flex flex-col p-4 gap-8 rounded-xl">
                        <div className="flex items-center gap-2">
                            <Checkbox
                                className="border-slate-500"
                                checked={checkboxes.nameVisible}
                                onCheckedChange={(checked) => handleCheckboxChange('nameVisible')(!!checked)}
                            />
                            <Label>My name is clearly visible on this document.</Label>
                        </div>
                        <div className="flex items-center gap-2">
                            <Checkbox
                                className="border-slate-500"
                                checked={checkboxes.certificationValid}
                                onCheckedChange={(checked) => handleCheckboxChange('certificationValid')(!!checked)}
                            />
                            <Label>The certification is still valid.</Label>
                        </div>
                        <div className="flex items-center gap-2">
                            <Checkbox
                                className="border-slate-500"
                                checked={checkboxes.recognizedAuthority}
                                onCheckedChange={(checked) => handleCheckboxChange('recognizedAuthority')(!!checked)}
                            />
                            <Label>This was issued by a recognized authority.</Label>
                        </div>
                    </div>
                </div>

                <div className="flex items-center justify-center">
                    <Button onClick={handleSaveDocument} disabled={isSubmitting}>
                        {isSubmitting ? "Saving..." : "Confirm & Save"}
                    </Button>
                </div>
            </div>
        </>
    )
}
export default CoachVerification