"use client";
import { getCMSContentOnce } from "@/hooks/getCMSContetOnce";
import { RootState } from "@/store";
import { usePathname } from "next/navigation";
import { useSelector } from "react-redux";
import AppBar from "./AppBar";
import RegistrationForm from "./auth/Register";
import Footer from "./footer";
import AboutSection from "./Home/aboutUs";
import AppDownloadSection from "./Home/appDownload";
import Banners from "./Home/Banners";
import BuiltForFamilies from "./Home/builtForFamilies";
import CoachSection from "./Home/coachSection";
import ConnectAthleteCommunity from "./Home/connectAthleteCommunity";
import ConnectAthleteEcosystem from "./Home/connectAthleteEcosystem";
import ConnectAthleteInAction from "./Home/connectAthleteInAction";
import ConnectJourney from "./Home/connectJourney";
import EcosystemSection from "./Home/ecosystem";
import HomeAnnouncement from "./Home/homeAnnouncement";
import HowItWorks from "./Home/howItWorks";
import OurImpactSection from "./Home/ourImpactSection";
import TechnologySection from "./Home/smartTechnology";
import TestimonialCarousel from "./Home/testimonialCarousel";
import UnlockPotential from "./Home/unlockPotentials";
import OurSports from "./Home/OurSports";

const LandingPage = () => {
  getCMSContentOnce();

  const {
    youthEcosystemSection,
    smartTechnologySection,
    ourCoachesSection,
    unlockSection,
    howItWorksSection,
    builtForFamiliesSection,
    testimonialSection,
    athleteInActionSection,
    athleteCommunitySection,
    ourImpactSection,
    homeAnnouncementSection,
    connectAthleteEcoSystemSection,
    mobileStoreSection,
    ourSportsSection,
    aboutSection
  } = useSelector((state: RootState) => state.homeCMS);
  const pathname = usePathname();

  // Define routes or prefixes where the banner should be hidden
  const hideBannerPaths = ["/register", "/terms", "/privacy-policy"];
  const shouldHideBanner = hideBannerPaths.some((path) =>
    pathname.startsWith(path)
  );

  return (
    <>
      <AppBar />
      {!shouldHideBanner && (
        <div className="w-full mt-[3.9rem] lg:mt-0 overflow-hidden">
          <Banners />
        </div>
      )}
      {homeAnnouncementSection?.isPublish && <HomeAnnouncement />}
      {/* <AboutSection /> */}
      {aboutSection?.isPublish && <AboutSection />}
      {youthEcosystemSection?.isPublish && <EcosystemSection />}
      {connectAthleteEcoSystemSection?.isPublish && <ConnectAthleteEcosystem />}
      {smartTechnologySection?.isPublish && <TechnologySection />}
      {howItWorksSection?.isPublish && <HowItWorks />}
      {builtForFamiliesSection?.isPublish && <BuiltForFamilies />}
      {ourCoachesSection?.isPublish && <CoachSection />}
      <RegistrationForm />
      {ourImpactSection?.isPublish && <OurImpactSection />}
      {testimonialSection?.isPublish && <TestimonialCarousel />}
      {athleteInActionSection?.isPublish && <ConnectAthleteInAction />}
      {athleteCommunitySection?.isPublish && <ConnectAthleteCommunity />}
      <ConnectJourney />
      {ourSportsSection?.isPublish && <OurSports />}
      {unlockSection?.isPublish && <UnlockPotential />}
      {mobileStoreSection?.isPublish && <AppDownloadSection />}
      <Footer />
    </>
  );
};

export default LandingPage;
