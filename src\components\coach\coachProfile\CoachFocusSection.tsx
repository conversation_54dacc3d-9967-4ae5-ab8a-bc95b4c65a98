'use client'

import MultiSelectWithChip from "@/components/common/MultiSelectWithChip"
import { Button } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { AppDispatch, RootState } from "@/store"
import { editCoachBackground, editCoachFocus, editCoachProfile, fetchCoachProfile, handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { Option } from "@/utils/interfaces"
import { useDispatch, useSelector } from "react-redux"

const CoachFocusSection = () => {
    const { toggleCoachingFocus, selectedFocuses, allCoachingFocusesList, toggleCoachingBackground, allCoachingBackground, selectedBackgrounds } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch<AppDispatch>()

    const handleToggleSection = (name) => {
        if (name === 'coachFocus') {
            dispatch(handleCoachInputChange({ name: 'toggleCoachingFocus', value: !toggleCoachingFocus }))
        } else if (name === 'coachBackground') {
            dispatch(handleCoachInputChange({ name: 'toggleCoachingBackground', value: !toggleCoachingBackground }))
        }
    }

    const handleChange = (newSelected: Option[], name) => {
        if (name === 'coachFocus') {
            dispatch(handleCoachInputChange({ name: 'selectedFocuses', value: [...newSelected] }))
        }
        else {
            dispatch(handleCoachInputChange({ name: 'selectedBackgrounds', value: [...newSelected] }))

        }
    };

    const saveCoachFocusBackgrnd = () => {

        const selectedBackgroundspayload = selectedBackgrounds.map(value => ({
            roleId: 3,
            coachId: 69,
            userId: 220,
            coachBackgroundId: value.value
        }));

        const selectedFocusespayload = selectedFocuses.map(value => ({
            roleId: 3,
            coachId: 69,
            userId: 220,
            coachFocusId: value.value
        }));

        // console.log(payload, "payload")

        dispatch(editCoachBackground(selectedBackgroundspayload));
        dispatch(editCoachFocus(selectedFocusespayload));
        dispatch(fetchCoachProfile())
        
    }

    return (
        <>
            <div className="flex flex-col gap-10 bg-slate-100 rounded-lg p-4">
                <div className="flex flex-col gap-5">
                    <div className="flex items-center justify-center gap-4">
                        <div className="flex flex-col items-center">
                            <h2 className="text-2xl font-bold text-primary">
                                Coaching Focus
                            </h2>
                            <span>What support do I offer?</span>
                        </div>
                        <Switch checked={toggleCoachingFocus} onCheckedChange={() => handleToggleSection('coachFocus')} />
                    </div>

                    {toggleCoachingFocus ? <div>
                        <Label className="text-lg">Coaching Focus {" "}
                            <span className="text-red-500">(NEW)</span>
                        </Label>
                        <MultiSelectWithChip
                            name="coachFocus"
                            value={selectedFocuses}
                            onChange={(e) => handleChange(e, "coachFocus")}
                            options={allCoachingFocusesList}
                            placeholder="Select Coach Focus..."
                        />
                    </div> : null}
                </div>

                <Separator />

                <div className="flex flex-col gap-5">
                    <div className="flex items-center justify-center gap-4">
                        <div className="flex flex-col items-center">
                            <h2 className="text-2xl font-bold text-primary">
                                Coaching Background
                            </h2>
                            <span>Where have I coached?</span>
                        </div>
                        <Switch checked={toggleCoachingBackground} onCheckedChange={() => handleToggleSection('coachBackground')} />
                    </div>

                    {toggleCoachingBackground ? <div>
                        <Label className="text-lg">Coaching Background {" "}
                            <span className="text-red-500">(NEW)</span>
                        </Label>
                        <MultiSelectWithChip
                            name="coachBackground"
                            value={selectedBackgrounds}
                            onChange={(e)=>handleChange(e,"coachBackground")}
                            options={allCoachingBackground}
                            placeholder="Select Coach Background..."
                        />
                    </div> : null}
                </div>

                <div className="flex items-end justify-end">
                    <Button className="w-24" onClick={() => saveCoachFocusBackgrnd()}>Save</Button>
                </div>
            </div>
        </>
    )
}
export default CoachFocusSection