import { AppDispatch } from "@/store";
import { fileUploadToS3 } from "@/store/slices/commonSlice";
import { Loader2, X } from "lucide-react";
import { useRef, useState } from "react";
import { MdCloudUpload } from "react-icons/md";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import { Button } from "../ui/button";
import FilePreview from "./FilePreview";

interface IProps {
    value: string | null; // File URL
    className?: string;
    acceptType: string[];
    onFileSelect?: (url: string | null) => void;
    handleRemove: () => void;
    disable?: boolean;
}

const UploadFiles = ({
    className = "",
    acceptType,
    onFileSelect,
    handleRemove,
    value,
    disable,
}: IProps) => {
    const inputRef = useRef<HTMLInputElement | null>(null);
    const dispatch = useDispatch<AppDispatch>();
    const [loading, setLoading] = useState(false)

    const handleClickUpload = () => {
        inputRef.current?.click();
    };

    const isAcceptedType = (file: File) => {
        return acceptType.some((type) =>
            file.type === type || file.type.startsWith(type.replace("*", ""))
        );
    };

    const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0] || null;

        if (file) {
            if (!isAcceptedType(file)) {
                toast.warning(`File type "${file.type}" is not accepted.`);
                e.target.value = "";
                return;
            }

            const formData = new FormData();
            formData.append("file", file, file.name);
            setLoading(true)

            try {
                const resultAction = await dispatch(fileUploadToS3(formData));
                const response = resultAction?.payload;
                console.log(resultAction, response)

                if (response?.url) {
                    onFileSelect?.(response.url);
                } else {
                    toast.error("File upload failed.");
                    onFileSelect?.(null);
                }
                setLoading(false)
            } catch (err) {
                console.error("Failed to upload file to S3", err);
                toast.error("Something went wrong while uploading the file.");
                onFileSelect?.(null);
                setLoading(false)
            } finally {
                e.target.value = "";
            }
        } else {
            onFileSelect?.(null);
        }
    };

    return (
        <>
            <div
                className={`relative aspect-auto border rounded-lg 
                overflow-hidden flex items-center justify-center bg-white ${className}`}
            >
                {value ? (
                    <>
                        <Button
                            size="icon"
                            variant="destructive"
                            onClick={handleRemove}
                            className="absolute top-2 right-2 z-10"
                        >
                            <X />
                        </Button>
                        <FilePreview s3FileLink={value} />
                    </>
                ) : (
                    <div
                        onClick={handleClickUpload}
                        className="w-full h-full cursor-pointer rounded-xl border-2 border-dashed border-gray-300
                        bg-white hover:bg-slate-50 transition-colors duration-300
                        flex flex-col items-center justify-center px-4 py-6 text-center group min-h-[140px]"
                    >
                        {loading ? (
                            <div className="flex flex-col items-center justify-center gap-2">
                                <Loader2 className="h-8 w-8 animate-spin text-slate-400" />
                                <span className="text-sm text-gray-400">Uploading...</span>
                            </div>
                        ) : (
                            <>
                                <MdCloudUpload size={35} className="text-blue-800" />
                                <span className="mt-2 text-sm font-medium text-gray-500 group-hover:text-slate-600">
                                    Click to upload file
                                </span>
                            </>
                        )}
                    </div>
                )}
            </div>

            <input
                ref={inputRef}
                type="file"
                accept={acceptType.join(",")}
                onChange={handleChange}
                className="hidden"
                disabled={disable}
            />
        </>
    );
};

export default UploadFiles;
