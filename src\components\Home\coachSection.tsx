"use client";
import React, { useState, useEffect, useRef } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import useEmblaCarousel from "embla-carousel-react";
import { Button } from "../ui/button";
import parse, { domToReact } from "html-react-parser";
import {
  titleParseOptions,
  shortDescriptionParseOptions,
  fileTitleParseOptions,
  fileDescriptionParseOptions,
  fileTitleCoachParseOptions,
} from "@/utils/parseOptions";
import { useDispatch } from "react-redux";
import { AppDispatch } from "@/store";
import { setFormUserType } from "@/store/slices/auth/registerSlice";
import { fetchRoleId } from "@/store/slices/auth/registerSlice";

const CoachSection = () => {
  const [selectedCoach, setSelectedCoach] = useState<any>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const [cursorPosition, setCursorPosition] = useState({ x: 0, y: 0 });
  const sectionRef = useRef<HTMLDivElement | null>(null);
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    align: "start",
  });

  const { ourCoachesSection, ourCoachesImages, loading } = useSelector(
    (state: RootState) => state.homeCMS
  );
  const dispatch = useDispatch<AppDispatch>();
  useEffect(() => {
    if (!emblaApi || isHovered) return;
    const autoplay = setInterval(() => emblaApi.scrollNext(), 2000);
    return () => clearInterval(autoplay);
  }, [emblaApi, isHovered]);

  const openDialog = (coach: any) => {
    setSelectedCoach(coach);
    setIsDialogOpen(true);
  };

  const closeDialog = () => {
    setIsDialogOpen(false);
    setSelectedCoach(null);
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    const rect = sectionRef.current?.getBoundingClientRect();
    if (rect) {
      setCursorPosition({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top,
      });
    }
  };

  // const scrollToRegistration = (role: string) => {
  //   const form = document.getElementById("registration");
  //   if (form) {
  //     form.scrollIntoView({ behavior: "smooth" });
  //     window.dispatchEvent(
  //       new CustomEvent("updateUserType", { detail: role.toLowerCase() })
  //     );
  //   }
  // };

  const scrollToRegistration = async () => {
    const section = document.getElementById("registration");
    if (section) {
      section.scrollIntoView({ behavior: "smooth" });
      dispatch(setFormUserType("coach")); // Update Redux state
      await dispatch(fetchRoleId("coach")); // Fetch and store roleId
    }
  };

  if (loading || !ourCoachesSection) return null;

  return (
    <section
      ref={sectionRef}
      onMouseMove={handleMouseMove}
      id="ourCoach"
      className="relative py-0 px-3 sm:px-8 md:px-16 lg:px-36 xl:px-56 bg-[#0D1D3A] overflow-hidden text-white"
    >
      {/* Orange radial cursor glow */}
      <div
        className="pointer-events-none absolute z-0 w-96 h-96 bg-orange-500/20 rounded-full blur-3xl opacity-50 transition-transform duration-200"
        style={{
          left: cursorPosition.x - 192,
          top: cursorPosition.y - 192,
        }}
      ></div>

      {/* Background decoration */}
      <div className="absolute inset-0 opacity-10 pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-purple-500 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto relative z-10">
        <div className="text-center mb-16">
          <h2 className="text-3xl text-center font-bold bg-gradient-to-r from-white via-blue-300 to-purple-400 bg-clip-text text-transparent leading-tight mb-6">
            {parse(ourCoachesSection.title || "", titleParseOptions)}
          </h2>
          <p className="cms-text max-w-2xl mx-auto text-gray-300">
            {parse(
              ourCoachesSection.description || "",
              shortDescriptionParseOptions
            )}
          </p>
        </div>

        <div
          className="overflow-hidden"
          ref={emblaRef}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div className="flex -ml-4">
            {ourCoachesImages.map((coach) => (
              <div
                key={coach.id}
                className="flex-[0_0_100%] md:flex-[0_0_50%] lg:flex-[0_0_25%] pl-4"
              >
                <div
                  className="group relative bg-[#1c2c47] rounded-2xl shadow-md hover:shadow-xl transition-all duration-500 border border-white/10 overflow-hidden transform hover:-translate-y-2 cursor-pointer h-[400px]"
                  onClick={() => {
                    // openDialog(coach);
                    if (coach.fileUrl) {
                      const match = coach.fileUrl.match(/href="([^"]+)"/);
                      const actualUrl = match?.[1];
                      if (actualUrl) {
                        window.open(actualUrl, "_blank");
                      }
                    }
                  }}
                >
                  <div className="relative h-64 overflow-hidden">
                    <img
                      src={coach.fileLocation}
                      alt="Coach"
                      className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent translate-y-full group-hover:translate-y-0 transition-transform duration-500"></div>
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent translate-y-full group-hover:translate-y-0 transition-transform duration-500"></div>
                    <div className="absolute top-4 right-0 bg-white text-white px-4 py-2 rounded-l-full text-sm font-semibold shadow-lg translate-x-1/2 group-hover:translate-x-0 transition-transform duration-300">
                      {parse(coach?.fileTag || "", fileTitleCoachParseOptions)}
                    </div>
                    <div className="absolute bottom-1 left-4 right-4 text-white translate-y-full group-hover:translate-y-0 transition-transform duration-500 delay-100">
                      {/* <h3 className="text-lg font-bold mb-1">
                        {parse(coach.fileTitle || "", fileTitleParseOptions)}
                      </h3>
                      <p className="text-sm opacity-90 line-clamp-2">
                        {parse(
                          coach.fileDescription || "",
                          fileDescriptionParseOptions
                        )}
                      </p> */}
                      {coach.fileUrl && (
                        <p className="text-sm text-blue-400 underline mt-1">
                          {parse(coach.fileUrl)}
                        </p>
                      )}
                    </div>
                  </div>

                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-white mb-2 group-hover:text-blue-400 transition-colors duration-300">
                      {parse(coach.fileTitle || "", fileTitleParseOptions)}
                    </h3>
                    <p className="cms-text text-sm text-gray-300 leading-relaxed line-clamp-2">
                      {parse(
                        coach.fileDescription || "",
                        fileDescriptionParseOptions
                      )}
                    </p>
                  </div>

                  {/* Shine effect */}
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-1000">
                    <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Dialog */}
        <Dialog open={isDialogOpen} onOpenChange={closeDialog}>
          <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto bg-[#1c2c47] text-white border border-white/10">
            <DialogHeader>
              <div className="flex items-center space-x-4">
                {selectedCoach && (
                  <>
                    <img
                      src={selectedCoach.fileLocation}
                      alt="Coach"
                      className="w-16 h-16 rounded-full object-cover"
                    />
                    <div>
                      <DialogTitle className="text-2xl font-bold text-white">
                        {parse(
                          selectedCoach.fileTitle || "",
                          fileTitleParseOptions
                        )}
                      </DialogTitle>
                    </div>
                  </>
                )}
              </div>
            </DialogHeader>
            {selectedCoach && (
              <DialogDescription className="cms-text text-base leading-relaxed mt-6 text-gray-300">
                {parse(
                  selectedCoach.fileDescription || "",
                  fileDescriptionParseOptions
                )}
              </DialogDescription>
            )}
          </DialogContent>
        </Dialog>
      </div>

      <div className="flex flex-col items-center justify-center text-center mt-6">
        <h4 className="text-4xl font-bold text-white mb-4">
          Want to shape the future of youth sports?
        </h4>
        <div className="flex flex-col sm:flex-row gap-4 sm:gap-8 mt-3 sm:mt-5">
          <Button
            variant="primaryGradient"
            size="xl"
            onClick={() => scrollToRegistration()}
          >
            Sign up
          </Button>
        </div>
      </div>

      <style>{`
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
    </section>
  );
};

export default CoachSection;
