'use client'

import SearchInput from "@/components/common/SearchInput"
import UploadFiles from "@/components/common/UploadFiles"
import { Button } from "@/components/ui/button"
import {
    Dialog, DialogClose, DialogContent,
    <PERSON><PERSON>Footer, <PERSON>alogHeader, <PERSON>alogT<PERSON>le, DialogTrigger
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RootState } from "@/store"
import { handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { EachSearchItem } from "@/utils/interfaces"
import { Plus } from "lucide-react"
import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"

const AddCertifications = () => {
    const {
        isAddCertificates,
        isEditCertificate,
        certificatesData,
        coachAddedCertificatesList
    } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch()

    const handleOnChange = (name: string, selected: EachSearchItem | File | string | null) => {
        dispatch(handleCoachInputChange({
            name: 'certificatesData',
            value: { ...certificatesData, [name]: selected }
        }))
    }

    const handleModal = () => {
        dispatch(handleCoachInputChange({ name: 'isAddCertificates', value: !isAddCertificates }))
    }

    const handleAddCertificationData = () => {
        if (isEditCertificate && certificatesData?.id) {
            // Update existing
            const updatedList = coachAddedCertificatesList.map(cert =>
                cert.id === certificatesData.id
                    ? { ...certificatesData, uploadedOn: cert.uploadedOn }
                    : cert
            )

            dispatch(handleCoachInputChange({
                name: 'coachAddedCertificatesList',
                value: updatedList
            }))
        } else {
            // Add new
            const newCertificate = {
                ...certificatesData,
                uploadedOn: new Date(),
                id: `${Date.now()}-${Math.floor(Math.random() * 10000)}`,
            }

            dispatch(handleCoachInputChange({
                name: 'coachAddedCertificatesList',
                value: [...coachAddedCertificatesList, newCertificate],
            }))
        }

        dispatch(handleCoachInputChange({ name: 'isAddCertificates', value: false }))
        dispatch(handleCoachInputChange({ name: 'isEditCertificate', value: false }))
        dispatch(handleCoachInputChange({ name: 'certificatesData', value: null }))
    }

    useEffect(() => {
        return () => {
            dispatch(handleCoachInputChange({ name: 'certificatesData', value: null }))
            dispatch(handleCoachInputChange({ name: 'isEditCertificate', value: false }))
        }
    }, [dispatch])


    return (
        <Dialog open={isAddCertificates} onOpenChange={handleModal}>
            <DialogTrigger asChild>
                <Button className="w-24">
                    <Plus /> Add
                </Button>
            </DialogTrigger>
            <DialogContent onInteractOutside={(event) => event.preventDefault()} className="w-[50vw] max-w-full max-h-[90%] p-8 flex flex-col">
                <DialogHeader>
                    <DialogTitle>
                        {isEditCertificate ? 'Edit Certification' : 'Add Certification'}
                    </DialogTitle>
                </DialogHeader>
                <div className="flex flex-col gap-8 p-8 overflow-y-auto">
                    <div className="grid gap-2">
                        <Label htmlFor="documentType">Document Type</Label>
                        <SearchInput
                            list={[
                                { value: 1, label: 'Certification' },
                                { value: 2, label: 'Award' },
                                { value: 3, label: 'Lincence' }
                            ]}
                            value={certificatesData?.documentType}
                            name="documentType"
                            placeholder="Select Document Type"
                            onChange={(name, selected) => handleOnChange('documentType', selected)}
                        />
                    </div>
                    <div className="grid gap-2">
                        <Label htmlFor="title">Title</Label>
                        <Input
                            id="title"
                            name="title"
                            value={certificatesData?.title || ''}
                            placeholder="Enter your title"
                            onChange={(e) => handleOnChange('title', e.target.value)}
                        />
                    </div>
                    <div className="grid gap-2">
                        <Label htmlFor="file">Upload PDF, Jpg, Jpeg, Mp4</Label>
                        <UploadFiles
                            value={certificatesData?.file!}
                            onFileSelect={(file) => handleOnChange('file', file)}
                            handleRemove={() => handleOnChange('file', null)}
                            acceptType={[]}
                            className="w-52"
                        />
                    </div>
                    <div className="grid gap-2">
                        <Label htmlFor="documentLink">Document Link</Label>
                        <Input
                            id="documentLink"
                            name="documentLink"
                            value={certificatesData?.documentLink || ''}
                            placeholder="Enter your Document Link"
                            onChange={(e) => handleOnChange('documentLink', e.target.value)}
                        />
                    </div>
                </div>
                <DialogFooter>
                    <DialogClose asChild>
                        <Button variant="outline">Cancel</Button>
                    </DialogClose>
                    <Button onClick={handleAddCertificationData}>
                        {isEditCertificate ? 'Update' : 'Save'}
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    )
}

export default AddCertifications
