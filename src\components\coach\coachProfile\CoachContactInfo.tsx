import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { AppDispatch, RootState } from "@/store"
import { editCoachProfile, handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { zodResolver } from "@hookform/resolvers/zod"
import { format } from "date-fns"
import { PencilLine } from "lucide-react"
import { useEffect, useState } from "react"
import { useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { z } from "zod"
import { toast } from "react-toastify"

const coachProfileSchema = z.object({
    firstName: z.string().min(1, "First name is required"),
    lastName: z.string().min(1, "Last name is required"),
    email: z.string().min(1, "Email is required").email("Invalid email format"),
    phone: z.string().optional(),
});


type FormData = z.infer<typeof coachProfileSchema>;

const CoachContactInfo = () => {
    const { toggleContactInfo, coachProfileData, isEditContactInfo, toggleEmail, togglePhone } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch<AppDispatch>()
    const [isSubmitting, setIsSubmitting] = useState(false)

    const handleOnChange = (name: string, value: boolean | string | any) => {
        dispatch(handleCoachInputChange({ name, value }))
    }

    const {
        register,
        handleSubmit,
        setValue,
        formState: { errors },
        reset,
    } = useForm<FormData>({
        resolver: zodResolver(coachProfileSchema),
        defaultValues: {
            firstName: coachProfileData?.firstName || '',
            lastName: coachProfileData?.lastName || '',
            email: coachProfileData?.user?.email || '',
            phone: coachProfileData?.phone || ''
        }
    });

    const handleCancel = () => {
        dispatch(handleCoachInputChange({ name: 'isEditContactInfo', value: !isEditContactInfo }))
        reset()
    }

    const handleEdit = () => {
        dispatch(handleCoachInputChange({ name: 'isEditContactInfo', value: true }));

        // Set form values manually when edit mode is triggered
        setValue('firstName', coachProfileData?.firstName || '');
        setValue('lastName', coachProfileData?.lastName || '');
        setValue('email', coachProfileData?.user?.email || '');
        setValue('phone', coachProfileData?.phone || '');
    };


    useEffect(() => {
        if (isEditContactInfo) {
            reset({
                firstName: coachProfileData?.firstName || '',
                lastName: coachProfileData?.lastName || '',
                email: coachProfileData?.user?.email || '',
                phone: coachProfileData?.phone || ''
            });
        }
    }, [isEditContactInfo, coachProfileData, reset]);

    const onSubmit = async (data: FormData) => {
        console.log("Submit Data:", data);

        setIsSubmitting(true);

        try {
            // Create API payload with coach profile data
            const apiPayload = {
                id: coachProfileData?.id,
                firstName: data.firstName,
                lastName: data.lastName,
                email: data.email,
                phone: data.phone,
                // Include other existing fields to avoid overwriting them
                bio: coachProfileData?.bio,
                achievements: coachProfileData?.achievements,
                lookingFor: coachProfileData?.lookingFor,
                genderYouCoach: coachProfileData?.genderYouCoach,
                virtualTraining: coachProfileData?.virtualTraining,
                websiteLink: coachProfileData?.websiteLink,
                blurb: coachProfileData?.blurb,
                // Add other fields as needed to preserve existing data
            };

            const resultAction = await dispatch(editCoachProfile(apiPayload));

            if (editCoachProfile.fulfilled.match(resultAction)) {
                toast.success("Contact information updated successfully");
                // Update local state for immediate UI update
                dispatch(handleCoachInputChange({ name: 'coachContactInfo', value: data }));
                handleCancel();
            } else {
                toast.error("Failed to update contact information");
            }
        } catch (error) {
            console.error("Failed to update contact info:", error);
            toast.error("Failed to update contact information");
        } finally {
            setIsSubmitting(false);
        }
    };


    console.log(coachProfileData,"coachProfileData")


    return (
        <>
            <div className='flex flex-col rounded-lg p-4 bg-slate-100'>
                <div className="flex items-center justify-center gap-5">
                    <h2 className="text-xl font-bold">Contact Info</h2>
                    <Switch
                        checked={toggleContactInfo}
                        onCheckedChange={(checked) => handleOnChange('toggleContactInfo', checked)} />
                </div>

                <div className="flex justify-end">
                    <Button size={'icon'} variant={'outline'}
                        onClick={handleEdit}>
                        <PencilLine />
                    </Button>
                </div>

                {toggleContactInfo &&
                    <>
                        <div className="bg-slate-100 rounded-lg p-5 flex flex-col gap-5">
                            {isEditContactInfo ? (
                                <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col gap-10">
                                    <div className="grid grid-cols-1 md:grid-cols-2 justify-center gap-6">
                                        <div className="flex flex-col gap-1">
                                            <Label>First Name</Label>
                                            <Input {...register("firstName")} placeholder="First Name" />
                                            {errors.firstName && (
                                                <p className="text-sm text-red-500">{errors.firstName.message}</p>
                                            )}
                                        </div>
                                        <div className="flex flex-col gap-1">
                                            <Label>Last Name</Label>
                                            <Input {...register("lastName")} placeholder="Last Name" />
                                            {errors.lastName && (
                                                <p className="text-sm text-red-500">{errors.lastName.message}</p>
                                            )}
                                        </div>
                                        <div className="flex flex-col gap-1">
                                            <div className="flex items-center gap-3">
                                                <Label>Email</Label>
                                                <Switch checked={toggleEmail}
                                                    onCheckedChange={(checked) => handleOnChange('toggleEmail', checked)} />
                                            </div>
                                            {toggleEmail && <Input {...register("email")} placeholder="Email" />}
                                            {errors.email && (
                                                <p className="text-sm text-red-500">{errors.email.message}</p>
                                            )}
                                            <div className="flex items-center gap-2 mt-3">
                                                <Button className="bg-blue-600 text-white" type="button">Verify My Email</Button>
                                                <Button variant={'link'} className="underline" type="button">Resend Link</Button>
                                            </div>
                                        </div>
                                        <div className="flex flex-col gap-1">
                                            <div className="flex items-center gap-3">
                                                <Label>Phone</Label>
                                                <Switch checked={togglePhone}
                                                    onCheckedChange={(checked) => handleOnChange('togglePhone', checked)} />
                                            </div>
                                            {togglePhone && <Input {...register("phone")} placeholder="Phone" />}
                                        </div>
                                    </div>
                                    <div className="flex justify-end gap-3">
                                        <Button className="w-24" variant={'outline'}
                                            onClick={handleCancel} disabled={isSubmitting}>Cancel</Button>
                                        <Button className="w-24" type="submit" disabled={isSubmitting}>
                                            {isSubmitting ? "Saving..." : "Save"}
                                        </Button>
                                    </div>
                                </form>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 items-center gap-8">
                                    <div className="flex flex-col gap-1">
                                        <Label>First Name</Label>
                                        <p className="h-10 px-3 py-2 text-sm rounded-md border border-slate-300 bg-slate-100 text-slate-900 shadow-sm focus:outline-none">
                                            {coachProfileData?.firstName || 'Not provided'}
                                        </p>
                                    </div>
                                    <div className="flex flex-col gap-1">
                                        <Label>Last Name</Label>
                                        <p className="h-10 px-3 py-2 text-sm rounded-md border border-slate-300 bg-slate-100 text-slate-900 shadow-sm focus:outline-none">
                                            {coachProfileData?.lastName || 'Not provided'}
                                        </p>
                                    </div>
                                    {coachProfileData?.phone && togglePhone && <div className="flex flex-col gap-1">
                                        <Label>Phone</Label>
                                        <p className="h-10 px-3 py-2 text-sm rounded-md border border-slate-300 bg-slate-100 text-slate-900 shadow-sm focus:outline-none">
                                            {coachProfileData?.phone}
                                        </p>
                                    </div>}
                                    {toggleEmail && <div className="flex flex-col gap-1">
                                        <Label>Email</Label>
                                        <p className="h-10 px-3 py-2 text-sm rounded-md border border-slate-300 bg-slate-100 text-slate-900 shadow-sm focus:outline-none">
                                            {coachProfileData?.user?.email || 'Not provided'}
                                        </p>
                                    </div>}
                                    <div className="flex flex-col gap-1">
                                        <Label>Latest T&C Accepted Date</Label>
                                        <p className="h-10 px-3 py-2 text-sm rounded-md border border-slate-300 bg-slate-100 text-slate-900 shadow-sm focus:outline-none">
                                            {coachProfileData?.updatedAt ? format(new Date(coachProfileData.updatedAt), 'MMM, dd yyyy hh:mm:ss') : format(new Date(), 'MMM, dd yyyy hh:mm:ss')}
                                        </p>
                                    </div>
                                </div>
                            )}
                        </div>
                    </>
                }
            </div >
        </>
    )
}
export default CoachContactInfo