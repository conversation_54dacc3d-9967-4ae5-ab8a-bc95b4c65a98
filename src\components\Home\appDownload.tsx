"use client";
import React, { useState } from "react";
import { Button } from "../ui/button";
import { RootState } from "@/store";
import { useSelector } from "react-redux";
import parse, { domToReact } from "html-react-parser";
import {
  titleParseOptions,
  shortDescriptionParseOptions,
} from "@/utils/parseOptions";
const AppDownloadSection = () => {
  const { mobileStoreSection, loading } = useSelector(
    (state: RootState) => state.homeCMS
  );

  const [email, setEmail] = useState("");
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleEmailSubmit = (e) => {
    e.preventDefault();
    if (email) {
      setIsSubmitted(true);
      setEmail("");
      // send the email to backend
      console.log("Email submitted:", email);
    }
  };

  return (
    <section className="py-20 px-3 sm:px-8 md:px-16 lg:px-36 xl:px-56 bg-[#0D1D3A] relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-40 h-40 bg-blue-400 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-32 h-32 bg-purple-400 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto relative">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-white mb-4">
            {mobileStoreSection?.title
              ? parse(mobileStoreSection.title, titleParseOptions)
              : "Coming Soon to Mobile"}
          </h2>
          <p className="text-gray-300 cms-text max-w-2xl mx-auto">
            {mobileStoreSection?.shortDescription
              ? parse(
                  mobileStoreSection.shortDescription,
                  shortDescriptionParseOptions
                )
              : "Take Connect Athlete with you wherever you go. Our mobile apps are coming soon with all the features you love."}
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8 max-w-2xl mx-auto mb-12">
          {/* iOS Card */}
          <div className="bg-gradient-to-br from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-2xl p-8 text-center border border-gray-600/50 relative overflow-hidden group">
            {/* Disabled overlay */}
            <div className="absolute inset-0 bg-gray-800/60 z-10"></div>

            <div className="relative z-20 opacity-50">
              <div className="flex justify-center mb-4">
                <img
                  className="w-24 h-24"
                  src="/landing-page-images/mockup.png"
                />
              </div>

              <h3 className="text-2xl font-bold text-white mb-2">iOS App</h3>
              <p className="text-gray-300 mb-6">Coming Soon</p>
              <button className="bg-gray-600 text-gray-300 px-8 py-3 rounded-lg font-medium cursor-not-allowed flex items-center justify-center mx-auto">
                <span className="mr-2">📱</span>
                App Store
              </button>
            </div>
          </div>

          {/* Android Card */}
          <div className="bg-gradient-to-br from-gray-800/80 to-gray-700/80 backdrop-blur-sm rounded-2xl p-8 text-center border border-gray-600/50 relative overflow-hidden group">
            {/* Disabled overlay */}
            <div className="absolute inset-0 bg-gray-800/60 z-10"></div>

            <div className="relative z-20 opacity-50">
              {/* <div className="text-6xl mb-4">🤖</div> */}
              <div className="flex justify-center mb-4">
                <img
                  className="w-24 h-24"
                  src="/landing-page-images/Phone_mockup.png"
                />
              </div>

              <h3 className="text-2xl font-bold text-white mb-2">
                Android App
              </h3>
              <p className="text-gray-300 mb-6">Coming Soon</p>
              <button className="bg-gray-600 text-gray-300 px-8 py-3 rounded-lg font-medium cursor-not-allowed flex items-center justify-center mx-auto">
                <span className="mr-2">🤖</span>
                Google Play
              </button>
            </div>
          </div>
        </div>

        {/* Email opt-in section */}
        {/* <div className="max-w-md mx-auto">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20">
            <h3 className="text-xl font-bold text-white mb-4 text-center">
              Subscribe for Updates
            </h3>

            {!isSubmitted ? (
              <form onSubmit={handleEmailSubmit} className="space-y-4">
                <div>
                  <input
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email address"
                    className="w-full px-4 py-3 rounded-lg bg-white/20 border border-white/30 text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-400 focus:border-transparent"
                    required
                  />
                </div>
                <Button
                  type="submit"
                  variant={"primaryGradient"}
                  className="w-full transition-all duration-300 transform hover:scale-105"
                >
                  Subscribe
                </Button>
              </form>
            ) : (
              <div className="text-center">
                <div className="text-4xl mb-4">✅</div>
                <p className="text-green-300 font-medium">
                  Thanks! We'll notify you when our mobile apps are available.
                </p>
              </div>
            )}
          </div>
        </div> */}
      </div>
    </section>
  );
};

export default AppDownloadSection;
