'use client'
import SportItem from "@/components/common/SportItem"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    DialogContent,
    Di<PERSON>Footer,
    Di<PERSON>Header,
    DialogTitle,
    DialogTrigger
} from "@/components/ui/dialog"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { AppDispatch, RootState } from "@/store"
import { handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { currentSeasonStatusList, fetchAllSpecialities, fetchAllSportLevels, fetchAllSports } from "@/store/slices/commonSlice"
import { zodResolver } from "@hookform/resolvers/zod"
import { Loader, Plus } from "lucide-react"
import { useEffect, useState } from "react"
import { Controller, DefaultValues, useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { z } from "zod"
import MultiSelectWithChip from "../../common/MultiSelectWithChip"
import SearchInput from "../../common/SearchInput"
import { Input } from "../../ui/input"
import { Label } from "../../ui/label"
import CoachSportDetails from "../coachSportProfile/CoachSportDetails"

const sportFormSchema = z.object({
    selectedSport: z
        .object({
            value: z.number(),
            label: z.string(),
        })
        .nullable()
        .optional()
        .refine((val) => val !== null && val !== undefined, {
            message: "Sport is required",
        }),
    isPrimarySport: z.boolean().optional(),
    yearsCoaching: z.string().optional(),
    currentSeason: z.any().optional(),
    selectedSportLevel: z.any().optional(),
    addedSportSpecilitiesList: z.any().optional(),
    currentTeam: z.string().optional(),
})

type SportFormValues = z.infer<typeof sportFormSchema>

const SportsInfo = () => {
    const { coachSelectedSportsList, toggleSportInfoSection } = useSelector((state: RootState) => state.coachProfile)
    const { allSportsList, allSportLevelList, allSpecilitiesList } = useSelector((state: RootState) => state.commonSlice)
    const dispatch = useDispatch<AppDispatch>()

    const [isModalOpen, setIsModalOpen] = useState(false)
    const [isLoading, setIsLoading] = useState(false)

    const defaultValues: DefaultValues<SportFormValues> = {
        selectedSport: undefined,
        isPrimarySport: false,
        yearsCoaching: "",
        currentSeason: null,
        selectedSportLevel: null,
        addedSportSpecilitiesList: [],
        currentTeam: "",
    }

    const {
        control,
        handleSubmit,
        reset,
        watch,
        formState: { errors },
    } = useForm<SportFormValues>({
        resolver: zodResolver(sportFormSchema),
        defaultValues,
    })

    const selectedSport = watch("selectedSport")

    useEffect(() => {
        dispatch(fetchAllSports())
        dispatch(fetchAllSportLevels())
    }, [dispatch])

    useEffect(() => {
        if (selectedSport?.value) {
            dispatch(fetchAllSpecialities(selectedSport.value))
        }
    }, [selectedSport, dispatch])

    const onSubmit = async (data: SportFormValues) => {
        setIsLoading(true)
        const userId = localStorage.getItem("userId")

        const payload = {
            userId,
            sportId: data?.selectedSport?.value,
            primarySport: data?.isPrimarySport,
            yearsCoaching: Number(data?.yearsCoaching || 0),
            currentTeam: data?.currentTeam,
            currentSeasonStatus: data?.currentSeason,
            levelId: data?.selectedSportLevel?.value,
            specialityIds: data?.addedSportSpecilitiesList?.map((each: any) => each?.value)
        }

        try {
            // Import the API function
            const { postCoachSportInfo } = await import("@/store/slices/coach/coachSportSlice")
            const resultAction = await dispatch(postCoachSportInfo(payload))

            if (postCoachSportInfo.fulfilled.match(resultAction)) {
                reset()
                // router.push(`${feProfileUrl}/${res?.sportName}/${res?.sportId}`)
                setIsModalOpen(false)
                // Optionally refresh the coach profile data
            }
        } catch (error) {
            console.error("Error adding sport:", error)
        } finally {
            setIsLoading(false)
        }
    }

    const handleToggle = (checked: boolean) => {
        dispatch(handleCoachInputChange({ name: 'toggleSportInfoSection', value: checked }))
    }

    console.log(coachSelectedSportsList," coachSelectedSportsList")

    return (
        <div className="flex flex-col gap-4 bg-slate-100 p-4 rounded-lg">
            <div className="flex flex-col items-center justify-center gap-4">
                <div className="flex flex-col items-center justify-center">
                    <div className="flex items-center gap-3">
                        <h3 className="font-bold text-xl text-center">Sports/Activities</h3>
                        <Switch checked={toggleSportInfoSection} onCheckedChange={handleToggle} />
                    </div>
                    <span className="text-center">(We recommend not more than 3 primary sports)</span>
                </div>
                <div className="self-end">
                    <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                        <DialogTrigger asChild>
                            <Button variant="outline" className="gap-2 border-slate-300 font-semibold hover:text-secondary">
                                <Plus />
                                Add Sport/Activity
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                            <DialogHeader>
                                <DialogTitle>Add Sport/Activity</DialogTitle>
                            </DialogHeader>
                       <CoachSportDetails />
                        </DialogContent>
                    </Dialog>
                </div>
            </div>

            {toggleSportInfoSection ? <div className="flex flex-col gap-5">
                {coachSelectedSportsList?.map(each => <SportItem roleId={3} key={each.id} item={each} />)}
            </div> : null}
        </div>
    )
}
export default SportsInfo