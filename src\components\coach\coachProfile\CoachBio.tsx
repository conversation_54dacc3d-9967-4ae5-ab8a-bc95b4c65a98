"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"
import { AppDispatch, RootState } from "@/store"
import { editCoachProfile, handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { PencilLine } from "lucide-react"
import { ChangeEvent } from "react"
import { useDispatch, useSelector } from "react-redux"
import { toast } from "react-toastify"


const CoachBio = () => {
    const { isBioEditable, toggleShortBio, shortBio, coachProfileData } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch<AppDispatch>()
    const coachBio = "Unlock your full potential by embracing learning every day. Small consistent steps build great habits. Stay curious, stay driven, and remember—growth starts with a single action.Unlock your full potential by embracing learning every day. Small consistent steps build great habits. Stay curious, stay driven, and remember—growth starts with a single action."

    const handleClickEdit = () => {
        dispatch(handleCoachInputChange({ name: 'isBioEditable', value: !isBioEditable }))
        dispatch(handleCoachInputChange({ name: 'shortBio', value: coachProfileData?.bio || '' }))
    }

    const handleToggleSection = () => {
        dispatch(handleCoachInputChange({ name: 'toggleShortBio', value: !toggleShortBio }))
    }

    const handleChange = (event: ChangeEvent<HTMLTextAreaElement>) => {
        dispatch(handleCoachInputChange({ name: 'shortBio', value: event.target.value }))
    }

    const handleClickSave = async() => {
        if (!shortBio) {
            toast.error("Please enter your short bio first");
            return;
        }

        const payload = {
            // Add required fields based on your API structure
            coachIntroBio: shortBio, // or the S3 URL from upload
            // Add other required fields like roleId, userId, etc.
        };

        try {
            // Replace with your actual API endpoint for saving coach video
            const resultAction = await dispatch(editCoachProfile(payload));
            if (editCoachProfile.fulfilled.match(resultAction)) {
                toast.success("Bio saved successfully");
                dispatch(handleCoachInputChange({ name: 'isBioEditable', value: false }))
                // Reset form or update UI as needed
            }
        } catch (error) {
            console.error("Failed to save bio:", error);
            toast.error("Failed to save bio");
        }
    }

    return (
        <div className="flex flex-col justify-center gap-4 bg-slate-100 p-4 rounded-lg">
            <div className="self-end">
                <Button variant={'outline'} size={'icon'} onClick={handleClickEdit}>
                    <PencilLine className="h-14 w-11" />
                </Button>
            </div>
            <div className="flex items-center justify-center gap-5">
                <h3 className="font-bold text-center text-xl">Short Bio</h3>
                <Switch checked={toggleShortBio} onCheckedChange={handleToggleSection} />
            </div>
            {toggleShortBio ? <div className="flex flex-col bg-slate-100 p-4 rounded-lg overflow-hidden">
                {isBioEditable ?
                    <div className="flex flex-col gap-8">
                        <Textarea
                            placeholder="Write your short bio"
                            rows={10}
                            maxLength={400}
                            onInput={(e) => {
                                const input = e.currentTarget;
                                if (input.value.length > 400) {
                                    input.value = input.value.slice(0, 400);
                                }
                            }}
                            value={shortBio}
                            onChange={handleChange}
                        />
                        <div className="flex justify-end gap-4">
                            <Button variant={'outline'} onClick={handleClickEdit}>Cancel</Button>
                            <Button disabled={!shortBio} onClick={handleClickSave}>Save</Button>
                        </div>
                    </div>
                    :
                    <p className="text-gray-800">{shortBio || coachBio}</p>
                }
            </div> : null}
        </div>
    )
}
export default CoachBio