'use client'
import { usePaymentData } from "@/contexts/PaymentContext";
import { useConfettiBlast } from "@/hooks/useConfettiBlast";
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo";
import { useTokenValues } from "@/hooks/useTokenValues";
import { AppDispatch, RootState } from "@/store";
import { handleUpdateTokenUserValues } from "@/store/slices/auth/loginSlice";
import { handleUpdatePremiumInput, postProcessPayment } from "@/store/slices/premiumSlice";
import { ROLES } from "@/utils/constants";
import { isObjectEmpty } from "@/utils/validations";
import { CheckCircle, Loader2, XCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { CreditCard, PaymentForm } from "react-square-web-payments-sdk";
import { Button } from "../ui/button";
import { Card } from "../ui/card";

const Payment = ({ planId }: { planId: number }) => {
    const { apiStatus } = useSelector((state: RootState) => state.premium)
    const dispatch = useDispatch<AppDispatch>()
    const router = useRouter();
    const { profileId } = useLocalStoredInfo()
    const { roleId, userId, } = useTokenValues()
    const { paymentData } = usePaymentData();
    const fireConfetti = useConfettiBlast()

    useEffect(() => {
        if (paymentData === null || paymentData === undefined || isObjectEmpty(paymentData)) {
            const roleNavigation = roleId === ROLES.ATHLETE ?
                `/athlete/premium-plan/checkout/${planId}`
                : `/business/premium-plan/checkout/${planId}`
            router.replace(roleNavigation)
        }
    }, [paymentData, roleId, router])

    useEffect(() => {
        const authToken = localStorage.getItem("token");
        if (!authToken) {
            router.push("/unauthorized");
            return;
        }
    }, [router]);


    const handleSubmitPayment = async (token: any) => {
        try {
            const payload = {
                roleId,
                userId,
                profileId,
                subscriptionPlanId: +planId,
                paymentInfo: { ...paymentData, nonce: token.token },
            };

            const resultAction = await dispatch(postProcessPayment(payload))
            if (postProcessPayment.fulfilled.match(resultAction)) {
                console.log(resultAction)
                if (resultAction?.payload) {
                    const { transaction_id, token, user, profileData, } = resultAction?.payload
                    dispatch(handleUpdateTokenUserValues({ token: token, user: user, profile: profileData }))
                    fireConfetti('payment-success-card')
                    localStorage.setItem(
                        "userInfo",
                        JSON.stringify(user)
                    );
                    localStorage.setItem(
                        "profileInfo",
                        JSON.stringify(profileData)
                    );
                    localStorage.setItem("profileId", profileData?.id);
                    localStorage.setItem("token", token);
                    localStorage.setItem("userId", user?.id);
                    localStorage.setItem("roleId", user?.roleId);
                    await router.replace(
                        roleId === ROLES.ATHLETE
                            ? `/athlete/active-premium/${transaction_id}`
                            : `/business/active-premium/${transaction_id}`)
                }
            }
        } catch (error) {
            console.error(error)
        }
    }

    return (
        <>
            <div className="flex flex-col gap-4">
                <div className="grid grid-cols-1 gap-6">
                    <Card className="p-6">
                        {apiStatus === 'postPaymentLoading' ?
                            <div className="flex flex-col items-center gap-4 text-center">
                                <div className="flex items-center justify-center">
                                    <Loader2 className="animate-spin text-primary w-12 h-12" />
                                </div>
                                <h3 className="text-primary text-xl font-semibold">
                                    Processing Your Payment
                                </h3>
                                <p className="text-gray-600 max-w-sm">
                                    Please do not refresh or navigate away while we confirm your payment.
                                    This may take a few seconds...
                                </p>
                            </div>
                            : apiStatus === 'postPaymentFailed' ? (
                                <div className="flex flex-col items-center gap-4 text-center">
                                    <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center">
                                        <XCircle className="text-red-600 w-8 h-8" />
                                    </div>
                                    <div className="flex items-center gap-1 text-red-600 text-2xl font-semibold">
                                        Payment Unsuccessful
                                    </div>
                                    <p className="text-gray-700">
                                        Unfortunately, We couldn’t process your payment.
                                        You can re-enter your card details and try again —
                                        or choose not to continue.
                                    </p>
                                    <Button onClick={() => dispatch(handleUpdatePremiumInput({ name: 'apiStatus', value: '' }))}>
                                        Retry
                                    </Button>
                                </div>
                            ) : apiStatus === 'postPaymentSuccess' ? (
                                <div id={'payment-success-card'} className="flex flex-col items-center gap-4 text-center">
                                    <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center">
                                        <CheckCircle className="text-green-600 w-10 h-10" />
                                    </div>
                                    <div className="text-green-600 text-3xl font-semibold">Payment Successful!</div>
                                    <p className="text-gray-700">
                                        Thank you for your payment.
                                        Your transaction has been processed successfully.
                                    </p>
                                </div>
                            ) : (
                                <>
                                    <div className="flex-1 flex flex-col gap-8">
                                        <h2 className="font-bold text-xl">Total Payment Amount: {' '}
                                            <span className="text-secondary">${paymentData?.totalAmountPaid || '-'}</span>
                                        </h2>
                                        <p>
                                            Upon successful payment confirmation, this amount
                                            will be charged to your credit card. The merchant's
                                            name on your credit card statement will appear as
                                            'Connect Athlete' (or 'Sigur Solutions').
                                        </p>
                                    </div>

                                    <Card className="shadow-md w-full p-3 hover:shadow-2xl transition-shadow duration-300 border border-gray-200">
                                        <PaymentForm
                                            applicationId={process.env.NEXT_PUBLIC_SQUARE_PAYMENT!}
                                            locationId={process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID!}
                                            cardTokenizeResponseReceived={handleSubmitPayment}
                                        >
                                            <CreditCard includeInputLabels />
                                        </PaymentForm>
                                    </Card>

                                    <div className="flex justify-center">
                                        <p className="text-red-600 max-w-sm text-center">
                                            Please do not refresh or navigate away while we confirm your payment.
                                        </p>
                                    </div>
                                </>
                            )}
                    </Card>
                </div>
            </div >
        </>
    )
}
export default Payment