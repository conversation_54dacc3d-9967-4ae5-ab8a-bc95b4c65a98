import AthleteSport from "@/components/athleteProfileSports/AthleteSport";
import ClientGuard from "@/components/ClientGuard";

type Props = {
    params: {
        slug: string;
        sportName: string;
        sportId: string;
    };
};

const AthleteSportPage = ({ params }: Props) => {

    return (
        <ClientGuard allowedRoles={[2]}>
            <AthleteSport params={params} />
        </ClientGuard>
    )
}
export default AthleteSportPage