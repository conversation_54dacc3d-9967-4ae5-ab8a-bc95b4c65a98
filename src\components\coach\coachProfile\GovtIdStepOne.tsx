import VerificationForm from "@/components/common/VerificationForm"
import {
    Card,
    CardContent,
    CardHeader, CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { RootState } from "@/store"
import { handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { EachSearchItem } from "@/utils/interfaces"
import { format } from "date-fns"
import { Download } from "lucide-react"
import { useDispatch, useSelector } from "react-redux"


const GovtIdStepOne = () => {
    const { govtIdData, addedGovtIdData } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch()

    const handleOnChange = (name: string, value: string | File | null | EachSearchItem) => {
        dispatch(handleCoachInputChange({ name: 'govtIdData', value: { ...govtIdData, [name]: value } }))
    }

    const handleSave = () => {
        dispatch(handleCoachInputChange({ name: 'govtIdData', value: null }))
        dispatch(handleCoachInputChange({ name: 'addedGovtIdData', value: { ...govtIdData } }))
    }

    const handleEdit = () => {
        dispatch(handleCoachInputChange({ name: 'govtIdData', value: { ...addedGovtIdData } }))
        dispatch(handleCoachInputChange({ name: 'addedGovtIdData', value: null }))
    }

    return (
        <>
            <div className="flex flex-col gap-8">
                {!addedGovtIdData ?
                    <VerificationForm
                        data={govtIdData}
                        handleOnChange={handleOnChange}
                    />
                    :
                    <div className="flex flex-col items-center justify-center">
                        <Card className="py-3 w-[70%]">
                            <CardHeader>
                                <CardTitle className="text-xl font-bold">{addedGovtIdData?.title}</CardTitle>
                                {/* <CardAction>
                                        <Button size="icon" variant="outline"
                                            onClick={handleEdit}
                                        >
                                            <PencilLine />
                                        </Button>
                                    </CardAction> */}
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-2 gap-3">
                                    <div className="flex flex-col gap-1">
                                        <p className="font-semibold">{addedGovtIdData?.documentType?.label}</p>
                                        <p><span className="font-semibold">Expiration Date:</span> <br />
                                            {addedGovtIdData?.expirationDate && format(new Date(addedGovtIdData?.expirationDate), 'MMM dd, yyyy')}</p>
                                    </div>
                                    <div className="flex flex-col justify-center items-center gap-2">
                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    {/* <img
                                                        src={
                                                            addedGovtIdData?.file?.type?.startsWith('image')
                                                                ? (URL.createObjectURL(addedGovtIdData?.file) || '/img-picture.svg')
                                                                : '/pdf-file.svg'
                                                        }
                                                        className={`${addedGovtIdData?.file?.type?.startsWith('image') ? 'w-full' : 'w-16'} h-full cursor-pointer`}
                                                    /> */}
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    {/* <p>{addedGovtIdData?.file?.name}</p> */}
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                        {/* {addedGovtIdData?.file && (
                                            <a
                                                href={URL.createObjectURL(addedGovtIdData?.file)}
                                                download={addedGovtIdData?.file?.name}
                                                className="flex items-center gap-1"
                                            >
                                                <Download className="w-4" />
                                                Download
                                            </a>
                                        )} */}
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                }
            </div>
        </>
    )
}
export default GovtIdStepOne