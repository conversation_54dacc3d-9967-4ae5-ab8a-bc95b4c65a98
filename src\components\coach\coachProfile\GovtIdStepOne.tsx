import VerificationForm from "@/components/common/VerificationForm"
import {
    Card,
    CardContent,
    CardHeader, CardTitle
} from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { AppDispatch, RootState } from "@/store"
import { handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { format } from "date-fns"
import { Download } from "lucide-react"
import { useDispatch, useSelector } from "react-redux"
import { Button } from "@/components/ui/button"
import { PencilLine } from "lucide-react"


const GovtIdStepOne = () => {
    const { govtIdData, addedGovtIdData } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch<AppDispatch>()

    const handleFormSubmit = (formData: any) => {
        // Save the form data to Redux state
        dispatch(handleCoachInputChange({ name: 'govtIdData', value: null }))
        dispatch(handleCoachInputChange({ name: 'addedGovtIdData', value: formData }))
    }

    const handleEdit = () => {
        dispatch(handleCoachInputChange({ name: 'govtIdData', value: { ...addedGovtIdData } }))
        dispatch(handleCoachInputChange({ name: 'addedGovtIdData', value: null }))
    }

    return (
        <>
            <div className="flex flex-col gap-8">
                {!addedGovtIdData ?
                    <div className="space-y-4">
                        <VerificationForm
                            data={govtIdData}
                            onSubmit={handleFormSubmit}
                            formId="govt-id-form"
                        />
                        <div className="flex justify-end gap-2">
                            <Button type="submit" form="govt-id-form">
                                Save Document
                            </Button>
                        </div>
                    </div>
                    :
                    <div className="flex flex-col items-center justify-center">
                        <Card className="py-3 w-[70%]">
                            <CardHeader className="flex flex-row items-center justify-between">
                                <CardTitle className="text-xl font-bold">{addedGovtIdData?.title}</CardTitle>
                                <Button size="icon" variant="outline" onClick={handleEdit}>
                                    <PencilLine className="w-4 h-4" />
                                </Button>
                            </CardHeader>
                            <CardContent>
                                <div className="grid grid-cols-2 gap-3">
                                    <div className="flex flex-col gap-2">
                                        <p className="font-semibold">{addedGovtIdData?.documentType?.label}</p>
                                        <p><span className="font-semibold">Description:</span> <br />
                                            {addedGovtIdData?.description}</p>
                                        <p><span className="font-semibold">Document Link:</span> <br />
                                            <a href={addedGovtIdData?.documentLink} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                                                {addedGovtIdData?.documentLink}
                                            </a>
                                        </p>
                                        <p><span className="font-semibold">Expiration Date:</span> <br />
                                            {addedGovtIdData?.expirationDate && format(new Date(addedGovtIdData?.expirationDate), 'MMM dd, yyyy')}</p>
                                    </div>
                                    <div className="flex flex-col justify-center items-center gap-2">
                                        <TooltipProvider>
                                            <Tooltip>
                                                <TooltipTrigger asChild>
                                                    <div className="w-16 h-16 bg-gray-200 rounded flex items-center justify-center">
                                                        <span className="text-xs text-gray-600">File</span>
                                                    </div>
                                                </TooltipTrigger>
                                                <TooltipContent>
                                                    <p>Uploaded Document</p>
                                                </TooltipContent>
                                            </Tooltip>
                                        </TooltipProvider>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                    </div>
                }
            </div>
        </>
    )
}
export default GovtIdStepOne