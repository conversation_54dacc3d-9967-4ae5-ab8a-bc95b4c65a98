import { RootState } from "@/store"
import { EachSearchItem, VerificationItem } from "@/utils/interfaces"
import { useSelector } from "react-redux"
import { Input } from "../ui/input"
import { Label } from "../ui/label"
import CommonCalender from "./CommonCalender"
import SearchInput from "./SearchInput"
import UploadFiles from "./UploadFiles"

interface IProps {
    step?: number;
    data: VerificationItem;
    handleOnChange: (name: string, value: string | File | null | EachSearchItem) => void;
}

const VerificationForm = ({ step, data, handleOnChange }: IProps) => {
    const { documentTypesList } = useSelector((state: RootState) => state.commonSlice)

    return (
        <>
            <div className="flex flex-col justify-center p-4 gap-10 bg-slate-200 rounded-xl">
                <div className="flex flex-col gap-2">
                    <Label>Title</Label>
                    <Input
                        value={data?.title}
                        name='title'
                        placeholder="Enter Title"
                        onChange={(event) => handleOnChange('title', event.target.value)}
                    />
                </div>
                <div className="flex flex-col gap-2">
                    <Label>Document Type</Label>
                    <SearchInput
                        list={documentTypesList}
                        name="Selct Document Type"
                        onChange={handleOnChange}
                        className=""
                        value={data?.documentType}
                        placeholder="Select Document Type"
                    />
                </div>
                {data?.documentType?.label?.toLowerCase() === 'other' ? <div className="flex flex-col gap-2">
                    <Label>Other Document Type</Label>
                    <Input value={data?.otherType} onChange={(event) => handleOnChange('otherType', event.target.value)} />
                </div> : null}
                <div className="flex flex-col gap-2">
                    <Label>Expiration Date {" "}
                        {step === 2 ? '(If document has expiration date) ' : null}
                    </Label>
                    <CommonCalender
                        placeholder="Pick Expiration Date"
                        mode="single"
                        dateValue={data?.expirationDate}
                        setDateFn={(date) => handleOnChange('expirationDate', date)}
                    />
                </div>
                <div className="flex flex-col gap-2">
                    <Label>Upload (PDF/JPG/PNG)</Label>
                    <UploadFiles
                        acceptType={['application/pdf', 'image/*']}
                        value={data?.file!}
                        handleRemove={() => handleOnChange('file', null)}
                        onFileSelect={(file) => handleOnChange('file', file)}
                        className="w-56"
                    />
                </div>
            </div>
        </>
    )
}
export default VerificationForm