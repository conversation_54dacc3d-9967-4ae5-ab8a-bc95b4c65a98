'use client'

import MultiSelectWithChip from "@/components/common/MultiSelectWithChip"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Switch } from "@/components/ui/switch"
import { RootState } from "@/store"
import { editCoachOffer, editCoachWhyonCA, handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { Option } from "@/utils/interfaces"
import { useDispatch, useSelector } from "react-redux"


const CoachQuestionary = () => {
    const { toggleWhyIAm, selectedWhyIAm, allWhyIAmOptions,
        toggleMyCoachingFocus, selectedMyCoachingFocuses,
        allMyCoachingFocusList, toggleWhatIOfferAsCoach,
        allCoachOfferList, selectedCoachOffer } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch()


    const handleToggleSection = (name) => {
        if (name === 'toggleWhyIAm') {
            dispatch(handleCoachInputChange({ name: 'toggleWhyIAm', value: !toggleWhyIAm }))
        } else if (name === 'toggleMyCoachingFocus') {
            dispatch(handleCoachInputChange({ name: 'toggleMyCoachingFocus', value: !toggleMyCoachingFocus }))
        } else if (name === 'toggleWhatIOfferAsCoach') {
            dispatch(handleCoachInputChange({ name: 'toggleWhatIOfferAsCoach', value: !toggleWhatIOfferAsCoach }))
        }
    }

    const handleChange = (name: string, newSelected: Option[]) => {
        dispatch(handleCoachInputChange({ name, value: [...newSelected] }))
    }; 

    const saveCoachWhyCAOffer = ()=> {

        const selectedWhyIAmPayload = selectedWhyIAm.map(value => ({
            roleId: 3,
            coachId: 69,
            userId: 220,
            pltfrmTagId: value.value
        }));
        const selectedMyCoachingFocusesPayload = selectedMyCoachingFocuses.map(value => ({
            roleId: 3,
            coachId: 69,
            userId: 220,
            coachFocusId: value.value
        }));
        const selectedCoachOfferPayload = selectedCoachOffer.map(value => ({
            roleId: 3,
            coachId: 69,
            userId: 220,
            offerTagId: value.value
        }));

        dispatch(editCoachWhyonCA(selectedWhyIAmPayload));
        // dispatch(editCoachFocus(selectedMyCoachingFocusesPayload));
        dispatch(editCoachOffer(selectedCoachOfferPayload));
    }

    return (
        <>
            <div className="flex flex-col gap-10 bg-slate-100 rounded-lg p-4">
                <div className="flex flex-col gap-5">
                    <div className="flex items-center justify-center gap-4">
                        <h2 className="text-xl font-bold text-primary">
                            Why I am on Connect Athlete
                        </h2>
                        <Switch checked={toggleWhyIAm} onCheckedChange={() => handleToggleSection('toggleWhyIAm')} />
                    </div>

                    {toggleWhyIAm ?
                        <div>
                            <MultiSelectWithChip
                                name="selectedWhyIAm"
                                value={selectedWhyIAm}
                                onChange={(newOption) => handleChange('selectedWhyIAm', newOption)}
                                options={allWhyIAmOptions}
                                placeholder="Select Options..."
                            />
                        </div> : null}
                </div>

                <Separator />

                <div className="flex flex-col gap-5">
                    <div className="flex items-center justify-center gap-4">
                        <h2 className="text-xl font-bold text-primary">
                            My Coaching Focus
                        </h2>
                        <Switch checked={toggleMyCoachingFocus} onCheckedChange={() => handleToggleSection('toggleMyCoachingFocus')} />
                    </div>

                    {toggleMyCoachingFocus ?
                        <div>
                            <MultiSelectWithChip
                                name="selectedMyCoachingFocuses"
                                value={selectedMyCoachingFocuses}
                                onChange={(newOption) => handleChange('selectedMyCoachingFocuses', newOption)}
                                options={allMyCoachingFocusList}
                                placeholder="Select Coaching Focuses..."
                            />
                        </div> : null}
                </div>

                <Separator />

                <div className="flex flex-col gap-5">
                    <div className="flex items-center justify-center gap-4">
                        <h2 className="text-xl font-bold text-primary">
                            What I Offer as a coach?
                        </h2>
                        <Switch checked={toggleWhatIOfferAsCoach} onCheckedChange={() => handleToggleSection('toggleWhatIOfferAsCoach')} />
                    </div>

                    {toggleWhatIOfferAsCoach ?
                        <MultiSelectWithChip
                            name="selectedCoachOffer"
                            value={selectedCoachOffer}
                            onChange={(newOption) => handleChange('selectedCoachOffer', newOption)}
                            options={allCoachOfferList}
                            placeholder="Select what offer..."
                        />
                        :
                        null
                    }
                </div>


                <div className="flex items-end justify-end">
                    <Button className="w-24" onClick={() => saveCoachWhyCAOffer()}>Save</Button>
                </div>
            </div>
        </>
    )
}
export default CoachQuestionary