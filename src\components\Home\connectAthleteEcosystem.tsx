"use client";
import useEmblaCarousel from "embla-carousel-react";
import { useEffect, useRef, useState } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import parse from "html-react-parser";
import {
  titleParseOptions,
  shortDescriptionParseOptions,
  fileTitleParseOptions
} from "@/utils/parseOptions";

const ConnectAthleteEcosystem = () => {
  const [isHovered, setIsHovered] = useState(false);
  const sectionRef = useRef<HTMLDivElement | null>(null);
  const [emblaRef, emblaApi] = useEmblaCarousel({
    loop: true,
    align: "start",
  });

  const {
    connectAthleteEcoSystemSection,
    connectAthleteEcoSystemImages,
    loading,
  } = useSelector((state: RootState) => state.homeCMS);

  useEffect(() => {
    if (!emblaApi || isHovered) return;
    const autoplay = setInterval(() => emblaApi.scrollNext(), 5000);
    return () => clearInterval(autoplay);
  }, [emblaApi, isHovered]);

  if (
    loading ||
    !connectAthleteEcoSystemSection ||
    connectAthleteEcoSystemImages.length === 0
  )
    return null;

  return (
    <section
      ref={sectionRef}
      id="ecosystem"
      className="py-20 bg-[#0D1D3A] px-4 md:px-16 lg:px-36 xl:px-56"
    >
      <div className="text-center mb-16">
        <h2 className="text-3xl text-center font-bold bg-gradient-to-r from-white via-blue-300 to-purple-400 bg-clip-text text-transparent leading-tight mb-6">
          {parse(connectAthleteEcoSystemSection.title || "", titleParseOptions)}
        </h2>
        <p className="max-w-3xl mx-auto text-gray-300 text-lg font-medium">
          {parse(
            connectAthleteEcoSystemSection.description || "",
            shortDescriptionParseOptions
          )}
        </p>
      </div>

      <div
        className="overflow-hidden"
        ref={emblaRef}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <div className="flex gap-6">
          {connectAthleteEcoSystemImages.map((card) => (
            <div
              key={card.id}
              className="min-w-[85%] md:min-w-[50%] lg:min-w-[33%] relative h-[420px] rounded-3xl overflow-hidden shadow-lg border border-white/10 group bg-[#13294B] hover:shadow-orange-500/20 transition-shadow duration-500"
            >
              {/* Logo */}
              {/* <div className="absolute top-4 left-4 z-20 bg-white/90 px-3 py-1 text-sm font-bold text-[#0D1D3A] rounded-full shadow">
                Connect Athlete
              </div> */}

              {/* Content */}
              <div className="relative z-20 h-full flex flex-col justify-center p-6 text-center text-white space-y-4">
                <h3 className="text-2xl font-bold">{parse(card.fileTitle || "", fileTitleParseOptions)}</h3>
                <p className="text-gray-200 text-md">
                  {parse(card.fileDescription)}
                </p>
              </div>

              {/* Hover Tagline */}
              {/* <div className="absolute inset-0 flex items-center justify-center bg-black/80 opacity-0 group-hover:opacity-100 transition-opacity duration-500 z-30 text-center px-6">
                <p className="text-orange-400 text-lg font-semibold">
                  {parse(card.fileTagline || "")}
                </p>
              </div> */}

              {/* Shine Effect */}
              <div className="absolute inset-0 z-40 pointer-events-none">
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-1000 ease-out" />
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ConnectAthleteEcosystem;
