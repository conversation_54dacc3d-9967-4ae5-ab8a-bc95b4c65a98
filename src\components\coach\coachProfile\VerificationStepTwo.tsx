import VerificationForm from "@/components/common/VerificationForm"
import { AppDispatch, RootState } from "@/store"
import { handleCoachInputChange } from "@/store/slices/coach/coachProfileSlice"
import { useDispatch, useSelector } from "react-redux"
import { Button } from "@/components/ui/button"

const VerificationStepTwo = () => {
    const { additionalDocList } = useSelector((state: RootState) => state.coachProfile)
    const dispatch = useDispatch<AppDispatch>()

    const handleFormSubmit = (index: number) => (formData: any) => {
        // Update the specific document in the additionalDocList
        const updatedList = [...additionalDocList]
        updatedList[index] = formData
        dispatch(handleCoachInputChange({ name: 'additionalDocList', value: updatedList }))
    }

    return (
        <>
            <div className="flex flex-col gap-7">
                {additionalDocList?.map((item, index) => (
                    <div key={index} className="space-y-4">
                        <h4 className="font-semibold text-lg">Additional Document {index + 1}</h4>
                        <VerificationForm
                            data={item}
                            onSubmit={handleFormSubmit(index)}
                            step={2}
                            formId={`additional-doc-form-${index}`}
                        />
                        <div className="flex justify-end">
                            <Button type="submit" form={`additional-doc-form-${index}`}>
                                Save Document {index + 1}
                            </Button>
                        </div>
                    </div>
                ))}
            </div>
        </>
    )
}
export default VerificationStepTwo