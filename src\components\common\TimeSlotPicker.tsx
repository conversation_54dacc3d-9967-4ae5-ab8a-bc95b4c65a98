import { Button } from "@/components/ui/button";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { Clock } from "lucide-react";
import { useState } from "react";

interface TimeSlot {
    id: number;
    time12hr: string;
    time24hr: string;
}

interface TimeSlotPickerProps {
    onChange: (name: string, timeSlotId: number | null) => void;
    name: string;
    timeSlots: TimeSlot[];
    value?: number | null;
    placeholder?: string;
}

const TimeSlotPicker: React.FC<TimeSlotPickerProps> = ({
    onChange,
    name,
    timeSlots,
    value,
    placeholder = "Select time",
}) => {
    const [isOpen, setIsOpen] = useState(false);

    const selectedTimeSlot = timeSlots.find(slot => slot.id === value);

    const handleTimeSlotSelect = (timeSlotId: number) => {
        onChange(name, timeSlotId);
        setIsOpen(false);
    };

    return (
        <Popover open={isOpen} onOpenChange={setIsOpen}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    className={cn(
                        "w-full justify-start text-left font-normal",
                        !selectedTimeSlot && "text-slate-500"
                    )}
                >
                    <Clock className="mr-2 h-4 w-4 text-slate-500" />
                    {selectedTimeSlot ? selectedTimeSlot.time12hr : placeholder}
                </Button>
            </PopoverTrigger>
            <PopoverContent className="p-0 w-[194px]" align="start">
                <div className="max-h-60 overflow-y-auto">
                    {timeSlots.map((slot) => (
                        <Button
                            key={slot.id}
                            variant="ghost"
                            className="w-full justify-start text-left font-normal hover:bg-slate-100"
                            onClick={() => handleTimeSlotSelect(slot.id)}
                        >
                            {slot.time12hr}
                        </Button>
                    ))}
                </div>
            </PopoverContent>
        </Popover>
    );
};

export default TimeSlotPicker;
